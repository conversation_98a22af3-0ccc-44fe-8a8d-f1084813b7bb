using System.Threading.Tasks;
using Imip.HotelFrontOffice.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Web.Controllers;

/// <summary>
/// Temporary debug controller for troubleshooting user synchronization
/// </summary>
[ApiController]
[Route("api/debug")]
[AllowAnonymous] // Temporary for debugging - remove in production
public class DebugController : AbpController
{
    private readonly IUserSynchronizationService _userSynchronizationService;

    public DebugController(IUserSynchronizationService userSynchronizationService)
    {
        _userSynchronizationService = userSynchronizationService;
    }

    /// <summary>
    /// Debug endpoint to inspect JWT token claims
    /// </summary>
    [HttpPost("token-claims")]
    public async Task<IActionResult> DebugTokenClaims([FromBody] DebugTokenRequest request)
    {
        if (string.IsNullOrEmpty(request.Token))
        {
            return BadRequest(new { Error = "Token is required" });
        }

        var result = await _userSynchronizationService.DebugTokenClaimsAsync(request.Token);
        return Ok(result);
    }

    /// <summary>
    /// Health check for user synchronization
    /// </summary>
    [HttpGet("user-sync-health")]
    public async Task<IActionResult> GetUserSyncHealth()
    {
        var health = await _userSynchronizationService.GetHealthAsync();
        return Ok(health);
    }
}

/// <summary>
/// Request model for debug token claims
/// </summary>
public class DebugTokenRequest
{
    public string Token { get; set; } = string.Empty;
}
